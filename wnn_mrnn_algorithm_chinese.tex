\documentclass{article}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{ctex}  % 支持中文

\begin{document}

\begin{algorithm}
\caption{WNN-MRNN批量训练过程}
\begin{algorithmic}[1]
\REQUIRE 一批训练数据 $\boldsymbol{x}$ 和真实标签 $\boldsymbol{y}$，模型参数 $w$，分解层数 $T$，MMRNN层数 $L$，以及超参数 $\lambda_1, \lambda_2$ 用于调节正则化项的强度。
\STATE 在初始卷积层中进行前向计算并获得特征图 $\mathcal{F}$。
\STATE 初始化MMRNN隐藏状态：$\mathcal{H}^{(0)} = \emptyset, \mathcal{C}^{(0)} = \emptyset$
\STATE 初始化分量特征列表：$\mathcal{S} = []$
\FOR{每个 $i \in[1, T]$}
    \STATE 确保序列长度为偶数：\textbf{如果} $|\mathcal{F}| \bmod 2 = 1$ \textbf{则} $\mathcal{F} = \text{Pad}(\mathcal{F}, 1)$
    \STATE 分裂：$\left[\mathcal{F}_{\text{偶}}, \mathcal{F}_{\text{奇}}\right] = \operatorname{Split}(\mathcal{F})$
    \STATE 更新：$L_i = \mathcal{F}_{\text{偶}} + U_i^{\text{LSTM}}\left(\mathcal{F}_{\text{奇}}\right)$
    \STATE 预测：$H_i = \mathcal{F}_{\text{奇}} - P_i^{\text{LSTM}}\left(L_i\right)$
    \STATE 特征提取：$H_i^{\text{特征}} = \operatorname{Conv1D}(H_i)$
    \STATE 添加到分量列表：$\mathcal{S} = \mathcal{S} \cup \{H_i^{\text{特征}}\}$
    \STATE 计算正则化项：
    \STATE $\lambda_H^{+} = \lambda_1 \cdot \operatorname{mean}\left(\left|H_i\right|\right)$
    \STATE $\lambda_L^{+} = \lambda_2 \cdot \operatorname{MSE}\left(\operatorname{mean}(L_i), \operatorname{mean}(\mathcal{F})\right)$
    \STATE 用 $L_i$ 更新 $\mathcal{F}$：$\mathcal{F} = L_i$
\ENDFOR
\STATE 处理最终低频分量：$L_T^{\text{特征}} = \operatorname{Conv1D}(L_T)$
\STATE 添加最终分量：$\mathcal{S} = \mathcal{S} \cup \{L_T^{\text{特征}}\}$
\STATE 对每个分量 $s_j \in \mathcal{S}$ 进行顺序MMRNN处理：
\FOR{每个分量 $j \in [1, |\mathcal{S}|]$}
    \STATE 投影输入：$s_j^{\text{投影}} = \operatorname{Linear}(s_j)$
    \FOR{每层 $\ell \in [1, L]$}
        \IF{$\ell = 1$}
            \STATE $\text{输入}_\ell = s_j^{\text{投影}}$
        \ELSE
            \STATE $\text{输入}_\ell = \text{输出}_{\ell-1}$
        \ENDIF
        \STATE 适配隐藏状态：$\mathcal{H}^{(\ell)} = \operatorname{Adapt}(\mathcal{H}^{(\ell)}, |\text{输入}_\ell|)$
        \STATE 适配细胞状态：$\mathcal{C}^{(\ell)} = \operatorname{Adapt}(\mathcal{C}^{(\ell)}, |\text{输入}_\ell|)$
        \STATE MSB处理：$\text{输出}_\ell = \operatorname{MSB}_\ell(\text{输入}_\ell, \mathcal{H}^{(\ell)})$
        \STATE 门控计算：$F_t = \sigma(\text{输出}_\ell)$
        \STATE 细胞候选：$\tilde{C}_t = \tanh(\text{输出}_\ell)$
        \STATE 更新细胞状态：$\mathcal{C}^{(\ell)} = F_t \odot (\mathcal{C}^{(\ell)} + \tilde{C}_t)$
        \STATE 更新隐藏状态：$\mathcal{H}^{(\ell)} = F_t \odot \tanh(\mathcal{C}^{(\ell)})$
    \ENDFOR
\ENDFOR

\STATE 全局平均池化：$f = \operatorname{GAP}(\mathcal{H}^{(L)})$
\STATE $\hat{y} = \operatorname{Softmax}(\operatorname{FC}(f))$ 并计算 $\lambda_{CE}$：
\STATE $-\sum_{i=1}^K y_i \log\left(\hat{y}_i\right)$
\STATE 最终损失为 $\lambda = \lambda_{CE} + \lambda_H + \lambda_L$。计算反向传播误差 $\frac{\partial \lambda}{\partial x}$ 并更新模型参数 $w = w - \eta \frac{\partial \lambda}{\partial w}$。
\end{algorithmic}
\end{algorithm}

\end{document}
