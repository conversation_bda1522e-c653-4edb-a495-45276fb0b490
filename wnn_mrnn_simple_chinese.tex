\documentclass{article}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{ctex}

\begin{document}

\begin{equation}
\begin{aligned}
&\text{算法 2 WNN-MRNN批量训练过程}\\
&\text{输入：一批训练数据 } \boldsymbol{x} \text{ 和真实标签 } \boldsymbol{y} \text{，模型}\\
&\text{参数 } w \text{，分解层数 } T \text{，以及}\\
&\lambda_1, \lambda_2 \text{ 是调节正则化项强度的超参数。}\\
&\text{在初始卷积层中进行前向计算并}\\
&\text{获得特征图 } \mathcal{F} \text{。}\\
&\text{初始化MMRNN状态：} \mathcal{H}^{(0)} = \emptyset, \mathcal{C}^{(0)} = \emptyset\\
&\text{对于每个 } i \in[1, T] \text{ 执行}\\
&\text{分裂：}[\mathcal{F}_{\text{偶}}, \mathcal{F}_{\text{奇}}]=\operatorname{Split}(\mathcal{F})\\
&\text{更新：} L_i=\mathcal{F}_{\text{偶}}+U_i^{\text{LSTM}}(\mathcal{F}_{\text{奇}})\\
&\text{预测：} H_i=\mathcal{F}_{\text{奇}}-P_i^{\text{LSTM}}(L_i)\\
&\text{特征提取：} H_i^{\text{特征}}=\operatorname{Conv1D}(H_i)\\
&\text{计算正则化项：}\\
&\lambda_H^{+}=\lambda_1 \cdot \operatorname{mean}(|H_i|);\\
&\lambda_L^{+}=\lambda_2 \cdot \operatorname{MSE}(\operatorname{mean}(L_i), \operatorname{mean}(\mathcal{F}))\\
&\text{用 } L_i \text{ 更新 } \mathcal{F}: \mathcal{F}=L_i\\
&\text{结束循环}\\
&\text{处理最终低频：} L_T^{\text{特征}}=\operatorname{Conv1D}(L_T)\\
&\text{对每个分量顺序执行MMRNN：}\\
&\text{对于每个分量 } j \in [H_1^{\text{特征}}, ..., H_T^{\text{特征}}, L_T^{\text{特征}}] \text{ 执行}\\
&\text{投影：} j^{\text{投影}}=\operatorname{Linear}(j)\\
&\text{MMRNN：} O_j, (\mathcal{H}^{(j)}, \mathcal{C}^{(j)})=\operatorname{MMRNN}(j^{\text{投影}}, (\mathcal{H}^{(j-1)}, \mathcal{C}^{(j-1)}))\\
&\text{结束循环}\\
&\text{全局池化：} f=\operatorname{GAP}(O_{\text{最终}})\\
&\hat{y}=\operatorname{Softmax}(\operatorname{FC}(f)) \text{ 并计算 } \lambda_{CE}:\\
&-\sum_{i=1}^K y_i \log(\hat{y}_i)\\
&\text{最终损失为 } \lambda=\lambda_{CE}+\lambda_H+\lambda_L \text{。计算反向}\\
&\text{传播误差 } \frac{\partial \lambda}{\partial x} \text{ 并更新模型参数}\\
&w=w-\eta \frac{\partial \lambda}{\partial w} \text{。}
\end{aligned}
\end{equation}

\end{document}
