import torch
import torch.nn as nn
import torch.nn.functional as F

class PredictUpdateOperator(nn.Module):
    """
    实现小波提升方案中的预测 P(·) 和更新 U(·) 操作符

    提升方案(Lifting Scheme)是一种构建双正交小波的方法，主要包含以下步骤：
    1. 分裂(Split): 将信号分为奇偶两组样本
    2. 预测(Predict): 使用偶数样本预测奇数样本，计算预测误差作为高频分量
    3. 更新(Update): 使用高频分量更新偶数样本，得到低频分量

    该类实现了预测和更新操作，使用卷积神经网络学习最优的小波滤波器
    """
    def __init__(self, channels):
        """
        初始化预测或更新操作符

        参数:
            channels: 输入特征的通道数
        """
        super(PredictUpdateOperator, self).__init__()
        # 第一个卷积层：使用3x1卷积核对周围的特征进行整合
        self.conv1 = nn.Conv1d(channels, channels, kernel_size=3, padding=0)
        # 第二个卷积层：使用1x1卷积核对通道间特征进行混合，保持空间尺寸不变
        self.conv2 = nn.Conv1d(channels, channels, kernel_size=1, padding=0)
        # ReLU激活函数增加非线性，inplace=True可节省内存
        self.relu = nn.ReLU(inplace=True)
        # Tanh激活函数用于限制输出范围在[-1,1]，使得小波系数具有零均值特性
        self.tanh = nn.Tanh()

    def forward(self, x):
        """
        前向传播计算

        参数:
            x: 输入特征，形状为 [batch_size, channels, length]

        返回:
            经过预测或更新操作的特征
        """
        # 使用反射填充保持序列长度不变，避免边界问题
        # 对序列两侧各填充1个元素
        x = F.pad(x, (1, 1), mode='reflect')
        # 应用第一个卷积层
        x = self.conv1(x)
        # 应用ReLU激活
        x = self.relu(x)
        # 应用第二个卷积层
        x = self.conv2(x)
        # 应用Tanh激活，得到有界的输出
        x = self.tanh(x)
        return x


class WaveletDecomposition(nn.Module):
    """
    实现小波分解模块，基于提升方案(Lifting Scheme)

    该模块对输入特征进行多级小波分解，分离高频和低频分量
    """
    def __init__(self, channels, decomposition_levels=3):
        """
        初始化小波分解模块

        参数:
            channels: 输入特征的通道数
            decomposition_levels: 小波分解的级数
        """
        super(WaveletDecomposition, self).__init__()
        self.decomposition_levels = decomposition_levels

        # 创建预测算子模块列表，每个级别一个
        self.predictors = nn.ModuleList([
            PredictUpdateOperator(channels) for _ in range(decomposition_levels)
        ])

        # 创建更新算子模块列表，每个级别一个
        self.updaters = nn.ModuleList([
            PredictUpdateOperator(channels) for _ in range(decomposition_levels)
        ])

    def split(self, x):
        """
        将信号分为奇偶两部分

        参数:
            x: 输入特征，形状为 [batch_size, channels, length]

        返回:
            偶数索引特征和奇数索引特征的元组
        """
        # ::2表示取偶数索引，1::2表示取奇数索引
        return x[:, :, ::2], x[:, :, 1::2]

    def forward(self, x, return_intermediate=False):
        """
        执行多级小波分解

        参数:
            x: 输入特征，形状为 [batch_size, channels, length]
            return_intermediate: 是否返回中间级别的低频分量

        返回:
            high_freqs: 各级的高频分量列表
            low_freq: 最终的低频分量
            intermediates: (可选) 中间级别的低频分量列表
        """
        batch_size = x.size(0)
        feature_map = x
        high_freqs = []
        intermediates = []

        # 多级分解
        for i in range(self.decomposition_levels):
            # 分裂：将当前特征分为奇偶两部分
            even, odd = self.split(feature_map)

            # 预测：使用偶数部分预测奇数部分，计算高频分量
            # H = odd - P(even)
            high_freq = odd - self.predictors[i](even)
            high_freqs.append(high_freq)

            # 更新：使用高频分量更新偶数部分，得到低频分量
            # L = even + U(H)
            low_freq = even + self.updaters[i](high_freq)

            if return_intermediate:
                intermediates.append(low_freq)

            # 更新特征图为当前的低频分量，进行下一级分解
            feature_map = low_freq

        if return_intermediate:
            return high_freqs, low_freq, intermediates
        else:
            return high_freqs, low_freq

    def compute_lifting_loss(self, x):
        """
        计算提升方案的正则化损失

        参数:
            x: 输入特征，形状为 [batch_size, channels, length]

        返回:
            loss_H: 高频分量损失，鼓励高频分量稀疏
            loss_L: 低频分量损失，保持低频分量与原始信号相似
        """
        loss_H = 0
        loss_L = 0

        feature_map = x

        # 多级分解并计算损失
        for i in range(self.decomposition_levels):
            # 存储原始特征均值用于损失计算
            original_mean = feature_map.mean()

            # 分裂
            even, odd = self.split(feature_map)

            # 预测
            high_freq = odd - self.predictors[i](even)

            # 更新
            low_freq = even + self.updaters[i](high_freq)

            # 计算正则化项
            # 高频损失：鼓励稀疏性（小波变换的主要特性）
            loss_H += torch.mean(torch.abs(high_freq))
            # 低频损失：保持低频均值与原始特征均值相似
            loss_L += torch.abs(low_freq.mean() - original_mean)

            # 更新特征图
            feature_map = low_freq

        return loss_H, loss_L
