\documentclass{article}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}

\begin{document}

\begin{equation}
\begin{aligned}
&\text{Algorithm 2 WNN-MRNN Training Procedure in a Batch}\\
&\text{Input: A batch of training data } \boldsymbol{x} \text{ and ground truth } \boldsymbol{y} \text{, model}\\
&\text{parameters } w \text{, the number of decomposition levels } T \text{, and}\\
&\lambda_1, \lambda_2 \text{ are the hyperparameters that tune the strength of}\\
&\text{the regularization terms.}\\
&\text{Forward calculation in initial convolutions layers and}\\
&\text{obtain feature map } \mathcal{F} \text{.}\\
&\text{Initialize MMRNN states: } \mathcal{H}^{(0)} = \emptyset, \mathcal{C}^{(0)} = \emptyset\\
&\text{for each } i \in[1, T] \text{ do}\\
&\text{Split: }[\mathcal{F}_{\text{even}}, \mathcal{F}_{\text{odd}}]=\operatorname{Split}(\mathcal{F})\\
&\text{Update: } L_i=\mathcal{F}_{\text{even}}+U_i^{\text{LSTM}}(\mathcal{F}_{\text{odd}})\\
&\text{Predict: } H_i=\mathcal{F}_{\text{odd}}-P_i^{\text{LSTM}}(L_i)\\
&\text{Feature extract: } H_i^{\text{feat}}=\operatorname{Conv1D}(H_i)\\
&\text{Compute regularization terms:}\\
&\lambda_H^{+}=\lambda_1 \cdot \operatorname{mean}(|H_i|);\\
&\lambda_L^{+}=\lambda_2 \cdot \operatorname{MSE}(\operatorname{mean}(L_i), \operatorname{mean}(\mathcal{F}))\\
&\text{Update } \mathcal{F} \text{ with } L_i: \mathcal{F}=L_i\\
&\text{end for}\\
&\text{Process final low freq: } L_T^{\text{feat}}=\operatorname{Conv1D}(L_T)\\
&\text{Sequential MMRNN for each component:}\\
&\text{for each component } j \in [H_1^{\text{feat}}, ..., H_T^{\text{feat}}, L_T^{\text{feat}}] \text{ do}\\
&\text{Project: } j^{\text{proj}}=\operatorname{Linear}(j)\\
&\text{MMRNN: } O_j, (\mathcal{H}^{(j)}, \mathcal{C}^{(j)})=\operatorname{MMRNN}(j^{\text{proj}}, (\mathcal{H}^{(j-1)}, \mathcal{C}^{(j-1)}))\\
&\text{end for}\\
&\text{Global pooling: } f=\operatorname{GAP}(O_{\text{final}})\\
&\hat{y}=\operatorname{Softmax}(\operatorname{FC}(f)) \text{ and compute } \lambda_{CE}:\\
&-\sum_{i=1}^K y_i \log(\hat{y}_i)\\
&\text{Final loss is } \lambda=\lambda_{CE}+\lambda_H+\lambda_L \text{. Compute the back-}\\
&\text{propagation error } \frac{\partial \lambda}{\partial x} \text{ and update the model parameters}\\
&w=w-\eta \frac{\partial \lambda}{\partial w} \text{.}
\end{aligned}
\end{equation}

\end{document}
