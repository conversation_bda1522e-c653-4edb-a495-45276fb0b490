import torch
import torch.nn as nn
import torch.nn.functional as F

from .wavelet import WaveletDecomposition, PredictUpdateOperator


class AWN(nn.Module):
    """
    自适应小波网络(Adaptive Wavelet Network)模型

    AWN模型结合卷积神经网络和小波分解，用于无线电信号调制识别
    主要包含以下组件：
    1. 初始卷积层：提取信号特征
    2. 小波分解层：使用提升方案进行多级小波分解
    3. 注意力机制：关注重要的频率分量
    4. 分类层：输出最终的调制类型预测
    """
    def __init__(self, num_classes, decomposition_levels=3, signal_length=128):
        """
        初始化AWN模型

        参数:
            num_classes: 分类类别数量
            decomposition_levels: 小波分解级数
            signal_length: 输入信号长度
            in_channels: 输入通道数
            wavelet_dim: 小波维度
            hidden_dim: 隐藏层维度
            dropout_rate: dropout率
            learnable_wavelets: 是否使用可学习小波
        """
        super(AW<PERSON>, self).__init__()
        self.decomposition_levels = decomposition_levels
        self.num_classes = num_classes
        self.signal_length = signal_length

        # 初始卷积层
        # 1. 2D卷积，处理IQ通道
        self.conv1 = nn.Conv2d(1, 64, kernel_size=(2, 7), padding=(0, 3))
        self.bn1 = nn.BatchNorm2d(64)

        # 2. 1D卷积序列
        self.conv2 = nn.Conv1d(64, 64, kernel_size=5, padding=2)
        self.bn2 = nn.BatchNorm1d(64)

        self.conv3 = nn.Conv1d(64, 64, kernel_size=3, padding=1)
        self.bn3 = nn.BatchNorm1d(64)

        self.conv4 = nn.Conv1d(64, 64, kernel_size=3, padding=1)
        self.bn4 = nn.BatchNorm1d(64)

        self.relu = nn.ReLU(inplace=True)

        # 小波分解模块
        self.wavelet = WaveletDecomposition(64, decomposition_levels)

        # 注意力机制
        total_channels = 64 * (decomposition_levels + 1)  # 高频 + 低频分量
        self.attention_fc1 = nn.Linear(total_channels, total_channels // 4)
        self.attention_fc2 = nn.Linear(total_channels // 4, total_channels // 9)

        # 分类层
        self.fc1 = nn.Linear(total_channels, 320)
        self.fc2 = nn.Linear(320, num_classes)

    def _process_input(self, x):
        """
        处理输入信号通过初始卷积层

        参数:
            x: 输入信号 [batch_size, 2, signal_length] 或 [batch_size, 1, 2, signal_length]

        返回:
            特征图 [batch_size, 64, signal_length/2^n]，其中n取决于池化操作
        """
        # 如有必要，添加通道维度: [B, 2, L] -> [B, 1, 2, L]
        if x.dim() == 3:
            x = x.unsqueeze(1)

        # 2D卷积处理IQ通道
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)

        # 去除多余维度: [B, 64, 1, L] -> [B, 64, L]
        x = x.squeeze(2)

        # 1D卷积序列
        x = self.conv2(x)
        x = self.bn2(x)
        x = self.relu(x)

        x = self.conv3(x)
        x = self.bn3(x)
        x = self.relu(x)

        x = self.conv4(x)
        x = self.bn4(x)
        x = self.relu(x)

        return x

    def forward(self, x):
        """
        前向传播

        参数:
            x: 输入信号 [batch_size, 2, signal_length]

        返回:
            logits: 分类预测 [batch_size, num_classes]
        """
        batch_size = x.size(0)

        # 处理输入通过初始卷积层
        feature_map = self._process_input(x)

        # 小波分解
        high_freqs, low_freq = self.wavelet(feature_map)

        # 对所有分量应用全局平均池化(GAP)并连接
        decomposed_features = []

        # 处理所有高频分量
        for high_freq in high_freqs:
            high_freq_gap = F.adaptive_avg_pool1d(high_freq, 1).view(batch_size, -1)
            decomposed_features.append(high_freq_gap)

        # 处理最终低频分量
        low_freq_gap = F.adaptive_avg_pool1d(low_freq, 1).view(batch_size, -1)
        decomposed_features.append(low_freq_gap)

        # 连接所有分解特征
        concat_features = torch.cat(decomposed_features, dim=1)

        # 注意力机制
        attention = self.attention_fc1(concat_features)
        attention = F.relu(attention)
        attention = self.attention_fc2(attention)
        attention = torch.sigmoid(attention)

        # 扩展注意力权重以匹配特征大小
        attention_expanded = F.interpolate(
            attention.unsqueeze(1),
            size=concat_features.size(1),
            mode='linear',
            align_corners=False
        ).squeeze(1)

        # 应用注意力权重
        weighted_features = concat_features * attention_expanded

        # 分类层
        x = self.fc1(weighted_features)
        x = self.relu(x)
        logits = self.fc2(x)

        return logits

    def compute_lifting_loss(self, x):
        """
        计算小波提升方案的正则化损失

        参数:
            x: 输入信号

        返回:
            loss_H: 高频损失
            loss_L: 低频损失
        """
        # 处理输入通过初始卷积层
        feature_map = self._process_input(x)

        # 计算小波分解损失
        loss_H, loss_L = self.wavelet.compute_lifting_loss(feature_map)

        return loss_H, loss_L
