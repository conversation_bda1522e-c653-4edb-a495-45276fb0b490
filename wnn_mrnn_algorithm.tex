Algorithm 2 WNN-MRNN Training Procedure in a Batch
Input: A batch of training data $\boldsymbol{x}$ and ground truth $\boldsymbol{y}$, model
    parameters $w$, the number of decomposition levels $T$, number of MMRNN layers $L$, and
    $\lambda_1, \lambda_2$ are the hyperparameters that tune the strength of
    the regularization terms.
    Forward calculation in initial convolutions layers and
    obtain feature map $\mathcal{F}$.
    Initialize MMRNN hidden states: $\mathcal{H}^{(0)} = \emptyset, \mathcal{C}^{(0)} = \emptyset$
    Initialize component feature list: $\mathcal{S} = []$
    for each $i \in[1, T]$ do
        Ensure even sequence length: if $|\mathcal{F}| \bmod 2 = 1$ then $\mathcal{F} = \text{Pad}(\mathcal{F}, 1)$
        Split: $\left[\mathcal{F}_{\text{even}}, \mathcal{F}_{\text{odd}}\right] = \operatorname{Split}(\mathcal{F})$
        Update: $L_i = \mathcal{F}_{\text{even}} + U_i^{\text{LSTM}}\left(\mathcal{F}_{\text{odd}}\right)$
        Predict: $H_i = \mathcal{F}_{\text{odd}} - P_i^{\text{LSTM}}\left(L_i\right)$
        Feature extraction: $H_i^{\text{feat}} = \operatorname{Conv1D}(H_i)$
        Add to component list: $\mathcal{S} = \mathcal{S} \cup \{H_i^{\text{feat}}\}$
        Compute regularization terms:
        $\lambda_H^{+} = \lambda_1 \cdot \operatorname{mean}\left(\left|H_i\right|\right)$
        $\lambda_L^{+} = \lambda_2 \cdot \operatorname{MSE}\left(\operatorname{mean}(L_i), \operatorname{mean}(\mathcal{F})\right)$
        Update $\mathcal{F}$ with $L_i$: $\mathcal{F} = L_i$
    end for
    Process final low frequency: $L_T^{\text{feat}} = \operatorname{Conv1D}(L_T)$
    Add final component: $\mathcal{S} = \mathcal{S} \cup \{L_T^{\text{feat}}\}$
    
    Sequential MMRNN processing for each component $s_j \in \mathcal{S}$:
    for each component $j \in [1, |\mathcal{S}|]$ do
        Project input: $s_j^{\text{proj}} = \operatorname{Linear}(s_j)$
        for each layer $\ell \in [1, L]$ do
            if $\ell = 1$ then
                $\text{input}_\ell = s_j^{\text{proj}}$
            else
                $\text{input}_\ell = \text{output}_{\ell-1}$
            end if
            Adapt hidden states: $\mathcal{H}^{(\ell)} = \operatorname{Adapt}(\mathcal{H}^{(\ell)}, |\text{input}_\ell|)$
            Adapt cell states: $\mathcal{C}^{(\ell)} = \operatorname{Adapt}(\mathcal{C}^{(\ell)}, |\text{input}_\ell|)$
            MSB processing: $\text{output}_\ell = \operatorname{MSB}_\ell(\text{input}_\ell, \mathcal{H}^{(\ell)})$
            Gate computation: $F_t = \sigma(\text{output}_\ell)$
            Cell candidate: $\tilde{C}_t = \tanh(\text{output}_\ell)$
            Update cell state: $\mathcal{C}^{(\ell)} = F_t \odot (\mathcal{C}^{(\ell)} + \tilde{C}_t)$
            Update hidden state: $\mathcal{H}^{(\ell)} = F_t \odot \tanh(\mathcal{C}^{(\ell)})$
        end for
    end for
    
    Global average pooling: $f = \operatorname{GAP}(\mathcal{H}^{(L)})$
    $\hat{y} = \operatorname{Softmax}(\operatorname{FC}(f))$ and compute $\lambda_{CE}$:
    $-\sum_{i=1}^K y_i \log\left(\hat{y}_i\right)$
    Final loss is $\lambda = \lambda_{CE} + \lambda_H + \lambda_L$. Compute the back-
    propagation error $\frac{\partial \lambda}{\partial x}$ and update the model parameters
    $w = w - \eta \frac{\partial \lambda}{\partial w}$.
