\documentclass{article}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithm}
\usepackage{algorithmic}

\begin{document}

\begin{algorithm}
\caption{WNN-MRNN Training Procedure in a Batch}
\begin{algorithmic}[1]
\REQUIRE A batch of training data $\boldsymbol{x}$ and ground truth $\boldsymbol{y}$, model parameters $w$, the number of decomposition levels $T$, number of MMRNN layers $L$, and $\lambda_1, \lambda_2$ are the hyperparameters that tune the strength of the regularization terms.
\STATE Forward calculation in initial convolutions layers and obtain feature map $\mathcal{F}$.
\STATE Initialize MMRNN hidden states: $\mathcal{H}^{(0)} = \emptyset, \mathcal{C}^{(0)} = \emptyset$
\STATE Initialize component feature list: $\mathcal{S} = []$
\FOR{each $i \in[1, T]$}
    \STATE Ensure even sequence length: \textbf{if} $|\mathcal{F}| \bmod 2 = 1$ \textbf{then} $\mathcal{F} = \text{Pad}(\mathcal{F}, 1)$
    \STATE Split: $\left[\mathcal{F}_{\text{even}}, \mathcal{F}_{\text{odd}}\right] = \operatorname{Split}(\mathcal{F})$
    \STATE Update: $L_i = \mathcal{F}_{\text{even}} + U_i^{\text{LSTM}}\left(\mathcal{F}_{\text{odd}}\right)$
    \STATE Predict: $H_i = \mathcal{F}_{\text{odd}} - P_i^{\text{LSTM}}\left(L_i\right)$
    \STATE Feature extraction: $H_i^{\text{feat}} = \operatorname{Conv1D}(H_i)$
    \STATE Add to component list: $\mathcal{S} = \mathcal{S} \cup \{H_i^{\text{feat}}\}$
    \STATE Compute regularization terms:
    \STATE $\lambda_H^{+} = \lambda_1 \cdot \operatorname{mean}\left(\left|H_i\right|\right)$
    \STATE $\lambda_L^{+} = \lambda_2 \cdot \operatorname{MSE}\left(\operatorname{mean}(L_i), \operatorname{mean}(\mathcal{F})\right)$
    \STATE Update $\mathcal{F}$ with $L_i$: $\mathcal{F} = L_i$
\ENDFOR
\STATE Process final low frequency: $L_T^{\text{feat}} = \operatorname{Conv1D}(L_T)$
\STATE Add final component: $\mathcal{S} = \mathcal{S} \cup \{L_T^{\text{feat}}\}$
\STATE Sequential MMRNN processing for each component $s_j \in \mathcal{S}$:
\FOR{each component $j \in [1, |\mathcal{S}|]$}
    \STATE Project input: $s_j^{\text{proj}} = \operatorname{Linear}(s_j)$
    \FOR{each layer $\ell \in [1, L]$}
        \IF{$\ell = 1$}
            \STATE $\text{input}_\ell = s_j^{\text{proj}}$
        \ELSE
            \STATE $\text{input}_\ell = \text{output}_{\ell-1}$
        \ENDIF
        \STATE Adapt hidden states: $\mathcal{H}^{(\ell)} = \operatorname{Adapt}(\mathcal{H}^{(\ell)}, |\text{input}_\ell|)$
        \STATE Adapt cell states: $\mathcal{C}^{(\ell)} = \operatorname{Adapt}(\mathcal{C}^{(\ell)}, |\text{input}_\ell|)$
        \STATE MSB processing: $\text{output}_\ell = \operatorname{MSB}_\ell(\text{input}_\ell, \mathcal{H}^{(\ell)})$
        \STATE Gate computation: $F_t = \sigma(\text{output}_\ell)$
        \STATE Cell candidate: $\tilde{C}_t = \tanh(\text{output}_\ell)$
        \STATE Update cell state: $\mathcal{C}^{(\ell)} = F_t \odot (\mathcal{C}^{(\ell)} + \tilde{C}_t)$
        \STATE Update hidden state: $\mathcal{H}^{(\ell)} = F_t \odot \tanh(\mathcal{C}^{(\ell)})$
    \ENDFOR
\ENDFOR

\STATE Global average pooling: $f = \operatorname{GAP}(\mathcal{H}^{(L)})$
\STATE $\hat{y} = \operatorname{Softmax}(\operatorname{FC}(f))$ and compute $\lambda_{CE}$:
\STATE $-\sum_{i=1}^K y_i \log\left(\hat{y}_i\right)$
\STATE Final loss is $\lambda = \lambda_{CE} + \lambda_H + \lambda_L$. Compute the back-propagation error $\frac{\partial \lambda}{\partial x}$ and update the model parameters $w = w - \eta \frac{\partial \lambda}{\partial w}$.
\end{algorithmic}
\end{algorithm}

\end{document}
